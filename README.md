# WeCoza Clients Plugin

A WordPress plugin for managing business clients with PostgreSQL backend, featuring MVC architecture and comprehensive client relationship management.

## Features

- **Client Management**: Create, edit, view, and soft-delete client records
- **MVC Architecture**: Clean separation of concerns with Controllers, Models, and Views
- **PostgreSQL Backend**: External PostgreSQL database with JSONB support for flexible data
- **Search & Filtering**: Advanced client search and filtering capabilities
- **File Uploads**: Document management with WordPress media integration
- **SETA Integration**: Full support for all South African SETA organizations
- **Bootstrap 5 UI**: Phoenix theme integration with responsive design
- **Shortcode Support**: Multiple shortcodes for embedding client functionality

## System Requirements

- WordPress 5.0+
- PHP 7.4+
- PostgreSQL 12+ (external database)
- Bootstrap 5 (Phoenix theme)

## Installation

1. Upload the plugin files to `/wp-content/plugins/wecoza-clients-plugin/`
2. Configure PostgreSQL connection settings in WordPress admin
3. Apply the database schema from `schema/clients_schema.sql`
4. Activate the plugin through WordPress admin

## Database Setup

The plugin uses an external PostgreSQL database. Apply the schema:

```bash
psql -h your-host -p 25060 -U username -d database < schema/clients_schema.sql
```

Connection settings are stored as WordPress options and must be configured during activation.

## Shortcodes

### Client Capture Form
```
[wecoza_capture_clients]
```
Renders a client creation/editing form with full validation.

### Client Display Table
```
[wecoza_display_clients per_page="10" show_search="true"]
```
Shows a paginated table of clients with search and filtering options.

### Single Client View
```
[wecoza_display_single_client id="123"]
```
Displays detailed information for a specific client.

## Architecture

### MVC Pattern
- **Controllers**: Handle requests, shortcodes, and AJAX endpoints
- **Models**: Manage data validation, database queries, and business logic
- **Views**: PHP templates for rendering HTML output
- **Services**: Shared functionality like database connections
- **Helpers**: Utility functions and view helpers

### Database Schema
- **clients**: Main table with JSONB fields for flexible data storage
- **client_meta**: Key-value metadata storage
- **client_notes**: Interaction history and notes
- Soft delete pattern with `deleted_at` timestamps

### File Structure
```
wecoza-clients-plugin/
   app/
      Controllers/        # Request handling
      Models/            # Data layer
      Views/             # Templates
      Services/          # Shared services
      Helpers/           # Utilities
      bootstrap.php      # Application initialization
   config/
      app.php           # Central configuration
   includes/             # WordPress integration
   schema/               # Database schemas
   assets/               # Static assets
```

## Configuration

All plugin settings are centralized in `config/app.php`:

- Validation rules
- SETA options
- AJAX endpoints
- Controller registration
- Database field definitions

## Security

### Capabilities
- `manage_wecoza_clients`: Full admin access
- `create_wecoza_clients`: Create new clients
- `edit_wecoza_clients`: Edit existing clients
- `delete_wecoza_clients`: Soft delete clients
- `view_wecoza_clients`: View client data
- `export_wecoza_clients`: Export to CSV

### Data Protection
- WordPress nonces for all forms
- Capability checks on all operations
- Prepared statements for database queries
- Input sanitization and validation
- Output escaping in views

## Development

### Adding New Fields
1. Update database schema
2. Add to Model `$fillable` array
3. Add validation rules in config
4. Update form views
5. Apply database changes

### Creating AJAX Endpoints
1. Define in `config/app.php`
2. Create controller method
3. Register in `registerAjaxHandlers()`
4. Implement client-side JavaScript

### Styling
All CSS must be added to the theme directory:
```
/wp-content/themes/wecoza_3_child_theme/includes/css/ydcoza-styles.css
```

## Client Data Fields

### Core Information
- Client name and registration details
- Contact person and communication preferences
- Address and location data
- Status tracking (Lead, Active, Lost, etc.)

### Business Details
- Company registration number
- SETA affiliation
- Branch relationships (parent/child)
- Industry and business type

### Flexible Data Storage
JSONB fields for complex data:
- `current_classes`: Array of active class enrollments
- `stopped_classes`: Historical class data
- `deliveries`: Delivery tracking information
- `assessments`: Assessment records
- `progressions`: Progress tracking

## License

This plugin is proprietary software developed for WeCoza business operations.

## Support

For technical support and bug reports, contact the development team.