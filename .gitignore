# Dependencies
/vendor/
/node_modules/

# Composer
composer.lock

# Build files
/build/
/dist/
*.min.js
*.min.css
*.map

# Logs
/logs/*
!logs/index.php
!logs/.htaccess
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE files
.idea/
.vscode/
*.sublime-project
*.sublime-workspace
.project
.settings/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# WordPress
wp-config.php
.htaccess

# Testing
/tests/coverage/
/tests/logs/
.phpunit.result.cache
phpunit.xml

# Environment files
.env
.env.*
!.env.example

# Backup files
*.bak
*.backup
*.old
*.orig
*.tmp

# Cache
/cache/
.sass-cache/

# Package files
*.zip
*.tar
*.tar.gz
*.rar

# Documentation build
/docs/_build/
/docs/.doctrees/
/legacy/

# Temporary files
*.temp
*.tmp
.temp/
.tmp/

# Local configuration
/config/local/
local-config.php

# MCP configuration with secrets
.mcp.json

.claude