# EditorConfig for WeCoza Agents Plugin
# https://editorconfig.org

# Top-most EditorConfig file
root = true

# Default settings for all files
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = tab
indent_size = 4

# PHP files
[*.php]
indent_style = tab
indent_size = 4

# JavaScript files
[*.js]
indent_style = tab
indent_size = 4

# CSS files
[*.css]
indent_style = tab
indent_size = 4

# JSON files
[*.json]
indent_style = space
indent_size = 2

# YAML files
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# Markdown files
[*.md]
trim_trailing_whitespace = false
indent_style = space
indent_size = 2

# XML files
[*.xml]
indent_style = space
indent_size = 2

# Composer files
[composer.json]
indent_style = space
indent_size = 4

# Package files
[package.json]
indent_style = space
indent_size = 2

# Configuration files
[.*rc]
indent_style = space
indent_size = 2

# Git files
[.git*]
indent_style = space
indent_size = 2

# Shell scripts
[*.sh]
indent_style = space
indent_size = 2

# Batch files
[*.bat]
end_of_line = crlf
indent_style = space
indent_size = 2

# Makefiles
[Makefile]
indent_style = tab

# Documentation
[*.txt]
indent_style = space
indent_size = 2

# SQL files
[*.sql]
indent_style = space
indent_size = 2

# WordPress specific
[*.pot]
indent_style = space
indent_size = 2

[*.po]
indent_style = space
indent_size = 2