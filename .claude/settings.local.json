{"permissions": {"allow": ["mcp__postgres-do__execute_sql", "Bash(ls:*)", "mcp__sequential-thinking__sequentialthinking", "mcp__task-master-ai__add_task", "Bash(git add:*)", "Bash(git rm:*)", "mcp__gemini-cli__chat", "Bash(git restore:*)", "Bash(rm:*)", "Bash(git commit:*)", "Bash(git push:*)", "Bash(find:*)"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["task-master-ai", "postgres-do", "gemini"]}