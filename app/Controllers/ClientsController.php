<?php

namespace WeCozaClients\Controllers;

use WeCozaClients\Models\ClientsModel;
use WeCozaClients\Helpers\ViewHelpers;

/**
 * Clients Controller for handling client operations
 *
 * @package WeCozaClients
 * @since 1.0.0
 */
class ClientsController {
    
    /**
     * Model instance (lazily loaded)
     *
     * @var ClientsModel|null
     */
    protected $model = null;
    
    /**
     * Constructor
     */
    public function __construct() {
        // Register shortcodes
        $this->registerShortcodes();
        
        // Register AJAX handlers
        $this->registerAjaxHandlers();
        
        // Enqueue assets
        add_action('wp_enqueue_scripts', array($this, 'enqueueAssets'));
    }

    /**
     * Get model instance on-demand
     *
     * @return ClientsModel
     */
    protected function getModel() {
        if ($this->model === null) {
            $this->model = new ClientsModel();
        }

        return $this->model;
    }

    /**
     * Get sites model instance
     *
     * @return SitesModel
     */
    protected function getSitesModel() {
        return $this->getModel()->getSitesModel();
    }
    
    /**
     * Register shortcodes
     */
    protected function registerShortcodes() {
        add_shortcode('wecoza_capture_clients', array($this, 'captureClientShortcode'));
        add_shortcode('wecoza_display_clients', array($this, 'displayClientsShortcode'));
        add_shortcode('wecoza_display_single_client', array($this, 'displaySingleClientShortcode'));
    }
    
    /**
     * Register AJAX handlers
     */
    protected function registerAjaxHandlers() {
        // Public AJAX handlers
        add_action('wp_ajax_wecoza_save_client', array($this, 'ajaxSaveClient'));
        add_action('wp_ajax_wecoza_get_client', array($this, 'ajaxGetClient'));
        add_action('wp_ajax_wecoza_delete_client', array($this, 'ajaxDeleteClient'));
        add_action('wp_ajax_wecoza_search_clients', array($this, 'ajaxSearchClients'));
        add_action('wp_ajax_wecoza_get_branch_clients', array($this, 'ajaxGetBranchClients'));
        add_action('wp_ajax_wecoza_export_clients', array($this, 'ajaxExportClients'));
        add_action('wp_ajax_wecoza_get_locations', array($this, 'ajaxGetLocations'));
        
        // Non-logged in users (if needed)
        add_action('wp_ajax_nopriv_wecoza_search_clients', array($this, 'ajaxSearchClients'));
        add_action('wp_ajax_nopriv_wecoza_get_locations', array($this, 'ajaxGetLocations'));
    }
    
    /**
     * Enqueue plugin assets
     */
    public function enqueueAssets() {
        global $post;
        
        // Check if we're on a page with our shortcodes
        if (!is_a($post, 'WP_Post')) {
            return;
        }
        
        $has_capture_form = has_shortcode($post->post_content, 'wecoza_capture_clients');
        $has_display_table = has_shortcode($post->post_content, 'wecoza_display_clients');
        $has_single_display = has_shortcode($post->post_content, 'wecoza_display_single_client');
        $nonce = wp_create_nonce('wecoza_clients_ajax');
        
        // Enqueue scripts based on shortcode presence
        if ($has_capture_form) {
            wp_enqueue_script(
                'wecoza-client-capture',
                \WeCozaClients\asset_url('js/client-capture.js'),
                array('jquery'),
                WECOZA_CLIENTS_VERSION,
                true
            );
            
            // Localize script
            wp_localize_script(
                'wecoza-client-capture',
                'wecoza_clients',
                $this->getLocalizationPayload($nonce, array(
                    'locations' => array(
                        'hierarchy' => array(),
                        'lazyLoad' => true,
                    ),
                ))
            );
        }
        
        if ($has_display_table) {
            wp_enqueue_script(
                'wecoza-clients-display',
                \WeCozaClients\asset_url('js/clients-display.js'),
                array('jquery'),
                WECOZA_CLIENTS_VERSION,
                true
            );
            
            wp_enqueue_script(
                'wecoza-client-search',
                \WeCozaClients\asset_url('js/client-search.js'),
                array('jquery'),
                WECOZA_CLIENTS_VERSION,
                true
            );
            
            // Localize script
            $localization = $this->getLocalizationPayload($nonce);
            wp_localize_script('wecoza-clients-display', 'wecoza_clients', $localization);
            wp_localize_script('wecoza-client-search', 'wecoza_clients', $localization);
        }
    }

    /**
     * Build localization payload for frontend scripts
     *
     * @param string $nonce Nonce value shared across scripts
     * @param array $overrides Additional data to merge
     * @return array
     */
    protected function getLocalizationPayload($nonce, array $overrides = array()) {
        $base = array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => $nonce,
            'actions' => array(
                'save' => 'wecoza_save_client',
                'get' => 'wecoza_get_client',
                'delete' => 'wecoza_delete_client',
                'search' => 'wecoza_search_clients',
                'branches' => 'wecoza_get_branch_clients',
                'export' => 'wecoza_export_clients',
                'locations' => 'wecoza_get_locations',
            ),
            'messages' => array(
                'form' => array(
                    'saving' => __('Saving client...', 'wecoza-clients'),
                    'saved' => __('Client saved successfully!', 'wecoza-clients'),
                    'error' => __('An error occurred. Please try again.', 'wecoza-clients'),
                ),
                'list' => array(
                    'confirmDelete' => __('Are you sure you want to delete this client?', 'wecoza-clients'),
                    'deleting' => __('Deleting client...', 'wecoza-clients'),
                    'deleted' => __('Client deleted successfully!', 'wecoza-clients'),
                    'exporting' => __('Preparing export...', 'wecoza-clients'),
                    'error' => __('An error occurred. Please try again.', 'wecoza-clients'),
                ),
                'general' => array(
                    'error' => __('Something went wrong. Please try again.', 'wecoza-clients'),
                ),
            ),
            'locations' => array(
                'hierarchy' => array(),
            ),
        );

        return array_replace_recursive($base, $overrides);
    }

    /**
     * AJAX handler to fetch locations lazily
     */
    public function ajaxGetLocations() {
        check_ajax_referer('wecoza_clients_ajax', 'nonce');

        $hierarchy = $this->getModel()->getLocationHierarchy();

        if (!is_array($hierarchy)) {
            wp_send_json_error(array(
                'message' => __('Unable to load locations right now. Please try again shortly.', 'wecoza-clients'),
            ), 500);
        }

        wp_send_json_success(array(
            'hierarchy' => $hierarchy,
        ));
    }
    
    /**
     * Client capture form shortcode
     *
     * @param array $atts Shortcode attributes
     * @return string
     */
    public function captureClientShortcode($atts) {
        // Check permissions
        if (!current_user_can('manage_wecoza_clients')) {
            return '<p>' . __('You do not have permission to create clients.', 'wecoza-clients') . '</p>';
        }
        
        $atts = shortcode_atts(array(
            'id' => 0,
        ), $atts);
        
        $client = null;
        $errors = array();
        $success = false;
        $submittedClient = array();
        $submittedSite = array();
        
        // Get client data if editing
        if ($atts['id']) {
            $client = $this->getModel()->getById($atts['id']);
            if (!$client) {
                return '<p>' . __('Client not found.', 'wecoza-clients') . '</p>';
            }
        }
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['wecoza_clients_form_nonce'])) {
            if (!wp_verify_nonce($_POST['wecoza_clients_form_nonce'], 'submit_clients_form')) {
                $errors[] = __('Security check failed. Please try again.', 'wecoza-clients');
            } else {
                $result = $this->handleFormSubmission($atts['id']);
                if ($result['success']) {
                    $success = true;
                    $client = $result['client'];
                } else {
                    $errors = $result['errors'];
                    if (!empty($result['data']['client']) && is_array($result['data']['client'])) {
                        $submittedClient = $result['data']['client'];
                    }
                    if (!empty($result['data']['site']) && is_array($result['data']['site'])) {
                        $submittedSite = $result['data']['site'];
                    }
                }
            }
        }

        if (!empty($submittedClient)) {
            $client = array_merge(is_array($client) ? $client : array(), $submittedClient);
        }
        
        // Get dropdown data
        $config = \WeCozaClients\config('app');
        $seta_options = $config['seta_options'];
        $status_options = $config['client_status_options'];

        $sitesData = array('head' => null, 'sub_sites' => array());
        if (!empty($client['id'])) {
            $sitesData = $this->getSitesModel()->getSitesByClient($client['id']);
        if (!empty($submittedSite)) {
            $sitesData['head'] = array_merge($sitesData['head'] ?? array(), $submittedSite);
            if (!empty($submittedSite['place_id'])) {
                $client['client_town_id'] = $submittedSite['place_id'];
            }
            if (!empty($submittedSite['address_line_1'])) {
                $client['client_street_address'] = $submittedSite['address_line_1'];
            }
            if (!empty($submittedSite['address_line_2'])) {
                $client['client_address_line_2'] = $submittedSite['address_line_2'];
            }
            if (!empty($submittedSite['site_name'])) {
                $client['site_name'] = $submittedSite['site_name'];
            }
        }
        }

        $selectedProvince = $client['client_province'] ?? ($client['client_location']['province'] ?? '');
        $selectedTown = $client['client_town'] ?? ($client['client_location']['town'] ?? '');
        $selectedSuburb = $client['client_suburb'] ?? ($client['client_location']['suburb'] ?? '');
        $selectedLocationId = !empty($client['client_town_id']) ? (int) $client['client_town_id'] : null;
        $selectedPostal = $client['client_postal_code'] ?? ($client['client_location']['postal_code'] ?? '');

        $hierarchy = $this->getSitesModel()->getLocationHierarchy();

        $locationData = array(
            'hierarchy' => $hierarchy,
            'selected' => array(
                'province' => $selectedProvince,
                'town' => $selectedTown,
                'suburb' => $selectedSuburb,
                'locationId' => $selectedLocationId,
                'postalCode' => $selectedPostal,
            ),
        );
        
        // Load view
        return \WeCozaClients\view('components/client-capture-form', array(
            'client' => $client,
            'errors' => $errors,
            'success' => $success,
            'seta_options' => $seta_options,
            'status_options' => $status_options,
            'location_data' => $locationData,
            'sites' => $sitesData,
        ));
    }
    
    /**
     * Display clients table shortcode
     *
     * @param array $atts Shortcode attributes
     * @return string
     */
    public function displayClientsShortcode($atts) {
        // Check permissions
        if (!current_user_can('view_wecoza_clients')) {
            return '<p>' . __('You do not have permission to view clients.', 'wecoza-clients') . '</p>';
        }
        
        $atts = shortcode_atts(array(
            'per_page' => 10,
            'show_search' => true,
            'show_filters' => true,
            'show_export' => true,
        ), $atts);
        
        // Get query parameters
        $page = isset($_GET['client_page']) ? max(1, intval($_GET['client_page'])) : 1;
        $search = isset($_GET['client_search']) ? sanitize_text_field($_GET['client_search']) : '';
        $status = isset($_GET['client_status']) ? sanitize_text_field($_GET['client_status']) : '';
        $seta = isset($_GET['client_seta']) ? sanitize_text_field($_GET['client_seta']) : '';
        
        // Build query parameters
        $params = array(
            'search' => $search,
            'status' => $status,
            'seta' => $seta,
            'limit' => $atts['per_page'],
            'offset' => ($page - 1) * $atts['per_page'],
        );
        
        // Get clients
        $clients = $this->getModel()->getAll($params);
        $total = $this->getModel()->count($params);
        $totalPages = ceil($total / $atts['per_page']);
        
        // Get statistics
        $stats = $this->getModel()->getStatistics();
        
        // Get filter options
        $config = \WeCozaClients\config('app');
        $seta_options = $config['seta_options'];
        $status_options = $config['client_status_options'];
        
        // Load view
        return \WeCozaClients\view('display/clients-display', array(
            'clients' => $clients,
            'total' => $total,
            'page' => $page,
            'totalPages' => $totalPages,
            'search' => $search,
            'status' => $status,
            'seta' => $seta,
            'stats' => $stats,
            'seta_options' => $seta_options,
            'status_options' => $status_options,
            'atts' => $atts,
        ));
    }
    
    /**
     * Display single client shortcode
     *
     * @param array $atts Shortcode attributes
     * @return string
     */
    public function displaySingleClientShortcode($atts) {
        // Check permissions
        if (!current_user_can('view_wecoza_clients')) {
            return '<p>' . __('You do not have permission to view clients.', 'wecoza-clients') . '</p>';
        }
        
        $atts = shortcode_atts(array(
            'id' => 0,
        ), $atts);
        
        // Get client ID from URL if not in attributes
        if (!$atts['id'] && isset($_GET['client_id'])) {
            $atts['id'] = intval($_GET['client_id']);
        }
        
        if (!$atts['id']) {
            return '<p>' . __('No client specified.', 'wecoza-clients') . '</p>';
        }
        
        // Get client data
        $client = $this->getModel()->getById($atts['id']);
        if (!$client) {
            return '<p>' . __('Client not found.', 'wecoza-clients') . '</p>';
        }
        
        $sites = $this->getSitesModel()->getSitesByClient($client['id']);

        return \WeCozaClients\view('display/single-client-display', array(
            'client' => $client,
            'sites' => $sites,
        ));
    }
    
    /**
     * Handle form submission
     *
     * @param int $clientId Client ID (for updates)
     * @return array
     */
    protected function handleFormSubmission($clientId = 0) {
        $payload = $this->sanitizeFormData($_POST);
        $clientData = $payload['client'];
        $siteData = $payload['site'];
        $contactData = $payload['contact'];
        $communicationType = $payload['communication_type'];
        $isNew = ((int) $clientId) <= 0;

        $errors = $this->getModel()->validate($clientData, $clientId);
        $siteErrors = $this->getSitesModel()->validateHeadSite($siteData);

        if ($siteErrors) {
            foreach ($siteErrors as $field => $message) {
                $errors['site_' . $field] = $message;
            }
        }

        if (!empty($errors)) {
            return array(
                'success' => false,
                'errors' => $errors,
                'data' => array(
                    'client' => $clientData,
                    'site' => $siteData,
                ),
            );
        }

        if (!$isNew) {
            $updated = $this->getModel()->update($clientId, $clientData);
            if (!$updated) {
                return array(
                    'success' => false,
                    'errors' => array('general' => __('Failed to update client. Please try again.', 'wecoza-clients')),
                    'data' => array(
                        'client' => $clientData,
                        'site' => $siteData,
                    ),
                );
            }
        } else {
            $clientId = $this->getModel()->create($clientData);
            if (!$clientId) {
                return array(
                    'success' => false,
                    'errors' => array('general' => __('Failed to create client. Please try again.', 'wecoza-clients')),
                    'data' => array(
                        'client' => $clientData,
                        'site' => $siteData,
                    ),
                );
            }
        }

        $siteData['site_name_fallback'] = $clientData['client_name'] ?? '';
        if (!empty($siteData['site_id']) && !$this->getSitesModel()->ensureSiteBelongsToClient($siteData['site_id'], $clientId)) {
            return array(
                'success' => false,
                'errors' => array('site_site_id' => __('Selected site does not belong to this client.', 'wecoza-clients')),
                'data' => array(
                    'client' => $clientData,
                    'site' => $siteData,
                ),
            );
        }

        $siteId = $this->getSitesModel()->saveHeadSite($clientId, $siteData);
        if (!$siteId) {
            return array(
                'success' => false,
                'errors' => array('general' => __('Failed to save site details. Please try again.', 'wecoza-clients')),
                'data' => array(
                    'client' => $clientData,
                    'site' => $siteData,
                ),
            );
        }

        $contactModel = $this->getModel()->getContactsModel();
        if (!empty($contactData['email']) || !empty($contactData['cellphone']) || !empty($contactData['telephone']) || !empty($contactData['name'])) {
            $contactData['site_id'] = $siteId;
            $contactModel->upsertPrimaryContact($clientId, $contactData);
        }

        if ($communicationType !== '') {
            $communicationsModel = $this->getModel()->getCommunicationsModel();
            $latestType = $communicationsModel->getLatestCommunicationType($clientId);
            if ($latestType !== $communicationType) {
                $communicationsModel->logCommunication($clientId, $siteId, $communicationType);
            }
        }

        $client = $this->getModel()->getById($clientId);

        return array(
            'success' => true,
            'client' => $client,
            'message' => $isNew ? __('Client created successfully!', 'wecoza-clients') : __('Client saved successfully!', 'wecoza-clients'),
        );
    }
    
    /**
     * Sanitize form data
     *
     * @param array $data Raw form data
     * @return array
     */
    protected function sanitizeFormData($data) {
        $client = array();

        $client['client_name'] = isset($data['client_name']) ? sanitize_text_field($data['client_name']) : '';
        $client['company_registration_nr'] = isset($data['company_registration_nr']) ? sanitize_text_field($data['company_registration_nr']) : '';
        $client['seta'] = isset($data['seta']) ? sanitize_text_field($data['seta']) : '';
        $client['client_status'] = isset($data['client_status']) ? sanitize_text_field($data['client_status']) : '';
        $client['financial_year_end'] = isset($data['financial_year_end']) ? sanitize_text_field($data['financial_year_end']) : '';
        $client['bbbee_verification_date'] = isset($data['bbbee_verification_date']) ? sanitize_text_field($data['bbbee_verification_date']) : '';

        $client['contact_person'] = isset($data['contact_person']) ? sanitize_text_field($data['contact_person']) : '';
        $client['contact_person_email'] = isset($data['contact_person_email']) ? sanitize_email($data['contact_person_email']) : '';
        $client['contact_person_cellphone'] = isset($data['contact_person_cellphone']) ? sanitize_text_field($data['contact_person_cellphone']) : '';
        $client['contact_person_tel'] = isset($data['contact_person_tel']) ? sanitize_text_field($data['contact_person_tel']) : '';
        $client['contact_person_position'] = isset($data['contact_person_position']) ? sanitize_text_field($data['contact_person_position']) : '';
        $client['client_communication'] = isset($data['client_communication']) ? sanitize_text_field($data['client_communication']) : '';

        $client['client_suburb'] = isset($data['client_suburb']) ? sanitize_text_field($data['client_suburb']) : '';
        $client['client_postal_code'] = isset($data['client_postal_code']) ? sanitize_text_field($data['client_postal_code']) : '';
        $client['client_province'] = isset($data['client_province']) ? sanitize_text_field($data['client_province']) : '';
        $client['client_town'] = isset($data['client_town_name']) ? sanitize_text_field($data['client_town_name']) : (isset($data['client_town']) ? sanitize_text_field($data['client_town']) : '');

        $placeId = isset($data['client_town_id']) ? (int) $data['client_town_id'] : 0;
        $client['client_town_id'] = $placeId;
        if ($placeId > 0) {
            $location = $this->getSitesModel()->getLocationById($placeId);
            if ($location) {
                $client['client_suburb'] = $location['suburb'] ?? $client['client_suburb'];
                $client['client_postal_code'] = $location['postal_code'] ?? $client['client_postal_code'];
                $client['client_province'] = $location['province'] ?? $client['client_province'];
                $client['client_town'] = $location['town'] ?? $client['client_town'];
            }
        }

        $site = array(
            'site_id' => isset($data['head_site_id']) ? (int) $data['head_site_id'] : 0,
            'site_name' => isset($data['head_site_name']) ? sanitize_text_field($data['head_site_name']) : '',
            'address_line_1' => isset($data['client_street_address']) ? sanitize_text_field($data['client_street_address']) : '',
            'address_line_2' => isset($data['client_address_line_2']) ? sanitize_text_field($data['client_address_line_2']) : '',
            'place_id' => $placeId,
        );

        $contact = array(
            'name' => $client['contact_person'],
            'email' => $client['contact_person_email'],
            'cellphone' => $client['contact_person_cellphone'],
            'telephone' => $client['contact_person_tel'],
            'position' => $client['contact_person_position'] ?? '',
        );

        return array(
            'client' => $client,
            'site' => $site,
            'contact' => $contact,
            'communication_type' => $client['client_communication'],
        );
    }
    
    /**
     * Handle file upload
     *
     * @param array $file File data from $_FILES
     * @return array
     */
    protected function handleFileUpload($file) {
        if (!function_exists('wp_handle_upload')) {
            require_once(ABSPATH . 'wp-admin/includes/file.php');
        }
        
        $upload_overrides = array(
            'test_form' => false,
            'mimes' => array(
                'pdf' => 'application/pdf',
                'doc' => 'application/msword',
                'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'xls' => 'application/vnd.ms-excel',
                'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            )
        );
        
        $movefile = wp_handle_upload($file, $upload_overrides);
        
        if ($movefile && !isset($movefile['error'])) {
            return array(
                'success' => true,
                'path' => $movefile['url'],
                'file' => $movefile['file'],
            );
        } else {
            return array(
                'success' => false,
                'error' => $movefile['error'] ?? __('File upload failed.', 'wecoza-clients'),
            );
        }
    }
    
    /**
     * AJAX: Save client
     */
    public function ajaxSaveClient() {
        // Verify nonce
        if (!check_ajax_referer('wecoza_clients_ajax', 'nonce', false)) {
            wp_die(json_encode(array('success' => false, 'message' => 'Security check failed.')));
        }
        
        // Check permissions
        $clientId = isset($_POST['id']) ? intval($_POST['id']) : 0;
        
        if (!current_user_can('manage_wecoza_clients')) {
            wp_die(json_encode(array('success' => false, 'message' => 'Permission denied.')));
        }
        
        // Handle form submission
        $result = $this->handleFormSubmission($clientId);
        
        wp_die(json_encode($result));
    }
    
    /**
     * AJAX: Get client
     */
    public function ajaxGetClient() {
        // Verify nonce
        if (!check_ajax_referer('wecoza_clients_ajax', 'nonce', false)) {
            wp_die(json_encode(array('success' => false, 'message' => 'Security check failed.')));
        }
        
        // Check permissions
        if (!current_user_can('view_wecoza_clients')) {
            wp_die(json_encode(array('success' => false, 'message' => 'Permission denied.')));
        }
        
        $clientId = isset($_GET['id']) ? intval($_GET['id']) : 0;
        if (!$clientId) {
            wp_die(json_encode(array('success' => false, 'message' => 'Invalid client ID.')));
        }
        
        $client = $this->getModel()->getById($clientId);
        if (!$client) {
            wp_die(json_encode(array('success' => false, 'message' => 'Client not found.')));
        }
        
        wp_die(json_encode(array('success' => true, 'client' => $client)));
    }
    
    /**
     * AJAX: Delete client
     */
    public function ajaxDeleteClient() {
        // Verify nonce
        if (!check_ajax_referer('wecoza_clients_ajax', 'nonce', false)) {
            wp_die(json_encode(array('success' => false, 'message' => 'Security check failed.')));
        }
        
        // Check permissions
        if (!current_user_can('manage_wecoza_clients')) {
            wp_die(json_encode(array('success' => false, 'message' => 'Permission denied.')));
        }
        
        $clientId = isset($_POST['id']) ? intval($_POST['id']) : 0;
        if (!$clientId) {
            wp_die(json_encode(array('success' => false, 'message' => 'Invalid client ID.')));
        }
        
        $success = $this->getModel()->delete($clientId);
        
        wp_die(json_encode(array(
            'success' => $success,
            'message' => $success ? 'Client deleted successfully.' : 'Failed to delete client.',
        )));
    }
    
    /**
     * AJAX: Search clients
     */
    public function ajaxSearchClients() {
        // Verify nonce
        if (!check_ajax_referer('wecoza_clients_ajax', 'nonce', false)) {
            wp_die(json_encode(array('success' => false, 'message' => 'Security check failed.')));
        }
        
        // Check permissions
        if (!current_user_can('view_wecoza_clients')) {
            wp_die(json_encode(array('success' => false, 'message' => 'Permission denied.')));
        }
        
        $search = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
        
        $clients = $this->getModel()->getAll(array(
            'search' => $search,
            'limit' => $limit,
        ));
        
        wp_die(json_encode(array('success' => true, 'clients' => $clients)));
    }

    /**
     * AJAX: Get branch clients
     */
    public function ajaxGetBranchClients() {
        // Verify nonce
        if (!check_ajax_referer('wecoza_clients_ajax', 'nonce', false)) {
            wp_die(json_encode(array('success' => false, 'message' => 'Security check failed.')));
        }
        
        // Check permissions
        if (!current_user_can('view_wecoza_clients')) {
            wp_die(json_encode(array('success' => false, 'message' => 'Permission denied.')));
        }
        
        $clientId = isset($_GET['client_id']) ? intval($_GET['client_id']) : 0;
        if (!$clientId) {
            wp_die(json_encode(array('success' => false, 'message' => 'Invalid client ID.')));
        }

        $sites = $this->getSitesModel()->getSitesByClient($clientId);

        wp_die(json_encode(array('success' => true, 'sites' => $sites)));
    }
    
    /**
     * AJAX: Export clients
     */
    public function ajaxExportClients() {
        // Verify nonce
        if (!check_ajax_referer('wecoza_clients_ajax', 'nonce', false)) {
            wp_die('Security check failed.');
        }
        
        // Check permissions
        if (!current_user_can('export_wecoza_clients')) {
            wp_die('Permission denied.');
        }
        
        // Get all clients
        $clients = $this->getModel()->getAll();
        
        // Set headers for CSV download
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename=clients-export-' . date('Y-m-d') . '.csv');
        
        // Create output
        $output = fopen('php://output', 'w');
        
        // Add BOM for UTF-8
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // Add headers
        $headers = array(
            'ID',
            'Client Name',
            'Company Registration Nr',
            'Contact Person',
            'Email',
            'Cellphone',
            'Town',
            'Status',
            'SETA',
            'Created Date',
        );
        fputcsv($output, $headers);
        
        // Add data
        foreach ($clients as $client) {
            $row = array(
                $client['id'],
                $client['client_name'],
                $client['company_registration_nr'],
                $client['contact_person'],
                $client['contact_person_email'],
                $client['contact_person_cellphone'],
                $client['client_town'],
                $client['client_status'],
                $client['seta'],
                $client['created_at'],
            );
            fputcsv($output, $row);
        }
        
        fclose($output);
        exit;
    }
}
