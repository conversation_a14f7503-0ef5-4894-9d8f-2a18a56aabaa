<div class="card shadow-none border my-3" data-component-card="data-component-card">
                <div class="card-header p-3 border-bottom">
                    <div class="row g-3 justify-content-between align-items-center mb-3">
                        <div class="col-12 col-md">
                            <h4 class="text-body mb-0" data-anchor="data-anchor" id="classes-table-header">
                                All Classes
                                <i class="bi bi-calendar-event ms-2"></i>
                            </h4>
                        </div>
                        <div class="search-box col-auto">
                          <form class="position-relative"><input class="form-control search-input search form-control-sm" type="search" placeholder="Search" aria-label="Search">
                            <svg class="svg-inline--fa fa-magnifying-glass search-box-icon" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="magnifying-glass" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M416 208c0 45.9-14.9 88.3-40 122.7L502.6 457.4c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L330.7 376c-34.4 25.2-76.8 40-122.7 40C93.1 416 0 322.9 0 208S93.1 0 208 0S416 93.1 416 208zM208 352a144 144 0 1 0 0-288 144 144 0 1 0 0 288z"></path></svg><!-- <span class="fas fa-search search-box-icon"></span> Font Awesome fontawesome.com -->
                          </form>
                        </div>
                        <div class="col-auto">
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="refreshClasses()">
                                    Refresh
                                    <i class="bi bi-arrow-clockwise ms-1"></i>
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="exportClasses()">
                                    Export
                                    <i class="bi bi-download ms-1"></i>
                                </button>
                            </div>
                        </div>
                    </div>
<!-- Summary strip -->
                  <div class="col-12">
                    <div class="scrollbar">
                      <div class="row g-0 flex-nowrap">
                        <div class="col-auto border-end pe-4">
                          <h6 class="text-body-tertiary">Total Classes : 45 <div class="badge badge-phoenix fs-10 badge-phoenix-success">+ 11</div></h6>
                        </div>
                        <div class="col-auto px-4 border-end">
                          <h6 class="text-body-tertiary">Active Classes : 45</h6>
                        </div>
                        <div class="col-auto px-4 border-end">
                          <h6 class="text-body-tertiary">SETA Funded : 39 <div class="badge badge-phoenix fs-10 badge-phoenix-success">+ 5</div></h6>
                        </div>
                        <div class="col-auto px-4 border-end">
                          <h6 class="text-body-tertiary">Exam Classes : 34 <div class="badge badge-phoenix fs-10 badge-phoenix-danger">+ 8</div></h6>
                        </div>
                        <div class="col-auto px-4">
                          <h6 class="text-body-tertiary">Unique Clients : 13 <div class="badge badge-phoenix fs-10 badge-phoenix-success">- 2</div></h6>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="card-body p-4 py-2">
                    <div class="table-responsive">
                        <span id="classes-search-status" class="badge badge-phoenix badge-phoenix-primary mb-2" style="display: none;"></span><table id="classes-table" class="table table-hover table-sm fs-9 mb-0 overflow-hidden">
                            <thead class="border-bottom">
                                <tr>
                                    <th scope="col" class="border-0 ps-4">
                                        ID
                                        <i class="bi bi-hash ms-1"></i>
                                    </th>
                                    <th scope="col" class="border-0">
                                        Client ID &amp; Name
                                        <i class="bi bi-building ms-1"></i>
                                    </th>
                                    <th scope="col" class="border-0">
                                        Type
                                        <i class="bi bi-tag ms-1"></i>
                                    </th>
                                    <th scope="col" class="border-0">
                                        Subject
                                        <i class="bi bi-book ms-1"></i>
                                    </th>
                                    <th scope="col" class="border-0">
                                        Start Date
                                        <i class="bi bi-calendar-date ms-1"></i>
                                    </th>
                                    <th scope="col" class="border-0">
                                        Agent ID &amp; Name
                                        <i class="bi bi-person ms-1"></i>
                                    </th>
                                    <th scope="col" class="border-0">
                                        Exam Class
                                        <i class="bi bi-mortarboard ms-1"></i>
                                    </th>
                                    <th scope="col" class="border-0">
                                        Status
                                        <i class="bi bi-activity ms-1"></i>
                                    </th>
                                    <th scope="col" class="border-0">
                                        SETA
                                        <i class="bi bi-award ms-1"></i>
                                    </th>
                                    <th scope="col" class="border-0 pe-4">
                                        Actions
                                        <i class="bi bi-gear ms-1"></i>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                                                <tr style="">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #55                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            5 : AgriGrow Farms                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            AET                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            COMM_NUM                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Oct 6, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Different agents or only one agent - show detailed format -->
                                            
                                                                                        <div>
                                                <strong>Initial:</strong>
                                                1 : Michael M. van der Berg                                                <i class="bi bi-person-badge ms-1"></i>
                                            </div>
                                                                                                                                </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=55" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=55" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="55" title="Delete Class" onclick="deleteClass(55)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #54                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            5 : AgriGrow Farms                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            AET                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            COMM_NUM                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Oct 6, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Different agents or only one agent - show detailed format -->
                                            
                                                                                        <div>
                                                <strong>Initial:</strong>
                                                2 : Thandi T. Nkosi                                                <i class="bi bi-person-badge ms-1"></i>
                                            </div>
                                                                                                                                </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            Not Exam
                                            <svg class="svg-inline--fa fa-ban ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="ban" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg="" style="transform-origin: 0.5em 0.5em;"><g transform="translate(256 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M367.2 412.5L99.5 144.8C77.1 176.1 64 214.5 64 256c0 106 86 192 192 192c41.5 0 79.9-13.1 111.2-35.5zm45.3-45.3C434.9 335.9 448 297.5 448 256c0-106-86-192-192-192c-41.5 0-79.9 13.1-111.2 35.5L412.5 367.2zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256z" transform="translate(-256 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            Not SETA
                                            <svg class="svg-inline--fa fa-ban ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="ban" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg="" style="transform-origin: 0.5em 0.5em;"><g transform="translate(256 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M367.2 412.5L99.5 144.8C77.1 176.1 64 214.5 64 256c0 106 86 192 192 192c41.5 0 79.9-13.1 111.2-35.5zm45.3-45.3C434.9 335.9 448 297.5 448 256c0-106-86-192-192-192c-41.5 0-79.9 13.1-111.2-35.5L412.5 367.2zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256z" transform="translate(-256 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=54" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=54" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="54" title="Delete Class" onclick="deleteClass(54)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #53                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            4 : FinanceFirst Corp                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            BA2                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            BA2LP10                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Oct 6, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Different agents or only one agent - show detailed format -->
                                            
                                                                                        <div>
                                                <strong>Initial:</strong>
                                                4 : Lerato L. Moloi                                                <i class="bi bi-person-badge ms-1"></i>
                                            </div>
                                                                                                                                </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=53" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=53" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="53" title="Delete Class" onclick="deleteClass(53)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #52                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            4 : FinanceFirst Corp                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            REALLL                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            RLN                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Sep 29, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Different agents or only one agent - show detailed format -->
                                            
                                                                                        <div>
                                                <strong>Initial:</strong>
                                                6 : Nomvula N. Dlamini                                                <i class="bi bi-person-badge ms-1"></i>
                                            </div>
                                                                                                                                </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            Not Exam
                                            <svg class="svg-inline--fa fa-ban ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="ban" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg="" style="transform-origin: 0.5em 0.5em;"><g transform="translate(256 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M367.2 412.5L99.5 144.8C77.1 176.1 64 214.5 64 256c0 106 86 192 192 192c41.5 0 79.9-13.1 111.2-35.5zm45.3-45.3C434.9 335.9 448 297.5 448 256c0-106-86-192-192-192c-41.5 0-79.9 13.1-111.2 35.5L412.5 367.2zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256z" transform="translate(-256 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            Not SETA
                                            <svg class="svg-inline--fa fa-ban ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="ban" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg="" style="transform-origin: 0.5em 0.5em;"><g transform="translate(256 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M367.2 412.5L99.5 144.8C77.1 176.1 64 214.5 64 256c0 106 86 192 192 192c41.5 0 79.9-13.1 111.2-35.5zm45.3-45.3C434.9 335.9 448 297.5 448 256c0-106-86-192-192-192c-41.5 0-79.9 13.1-111.2-35.5L412.5 367.2zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256z" transform="translate(-256 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=52" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=52" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="52" title="Delete Class" onclick="deleteClass(52)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #51                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            5 : AgriGrow Farms                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            BA2                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            BA2LP2                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Sep 22, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Different agents or only one agent - show detailed format -->
                                            
                                                                                        <div>
                                                <strong>Initial:</strong>
                                                3 : Rajesh R. Patel                                                <i class="bi bi-person-badge ms-1"></i>
                                            </div>
                                                                                                                                </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            Not Exam
                                            <svg class="svg-inline--fa fa-ban ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="ban" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg="" style="transform-origin: 0.5em 0.5em;"><g transform="translate(256 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M367.2 412.5L99.5 144.8C77.1 176.1 64 214.5 64 256c0 106 86 192 192 192c41.5 0 79.9-13.1 111.2-35.5zm45.3-45.3C434.9 335.9 448 297.5 448 256c0-106-86-192-192-192c-41.5 0-79.9 13.1-111.2 35.5L412.5 367.2zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256z" transform="translate(-256 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            Not SETA
                                            <svg class="svg-inline--fa fa-ban ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="ban" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg="" style="transform-origin: 0.5em 0.5em;"><g transform="translate(256 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M367.2 412.5L99.5 144.8C77.1 176.1 64 214.5 64 256c0 106 86 192 192 192c41.5 0 79.9-13.1 111.2-35.5zm45.3-45.3C434.9 335.9 448 297.5 448 256c0-106-86-192-192-192c-41.5 0-79.9 13.1-111.2-35.5L412.5 367.2zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256z" transform="translate(-256 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=51" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=51" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="51" title="Delete Class" onclick="deleteClass(51)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #50                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            5 : AgriGrow Farms                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            GETC                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            NL4                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Aug 1, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Different agents or only one agent - show detailed format -->
                                            
                                                                                        <div>
                                                <strong>Initial:</strong>
                                                8 : Zanele Z. Mthembu                                                <i class="bi bi-person-badge ms-1"></i>
                                            </div>
                                                                                                                                </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            Not Exam
                                            <svg class="svg-inline--fa fa-ban ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="ban" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg="" style="transform-origin: 0.5em 0.5em;"><g transform="translate(256 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M367.2 412.5L99.5 144.8C77.1 176.1 64 214.5 64 256c0 106 86 192 192 192c41.5 0 79.9-13.1 111.2-35.5zm45.3-45.3C434.9 335.9 448 297.5 448 256c0-106-86-192-192-192c-41.5 0-79.9 13.1-111.2 35.5L412.5 367.2zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256z" transform="translate(-256 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            Not SETA
                                            <svg class="svg-inline--fa fa-ban ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="ban" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg="" style="transform-origin: 0.5em 0.5em;"><g transform="translate(256 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M367.2 412.5L99.5 144.8C77.1 176.1 64 214.5 64 256c0 106 86 192 192 192c41.5 0 79.9-13.1 111.2-35.5zm45.3-45.3C434.9 335.9 448 297.5 448 256c0-106-86-192-192-192c-41.5 0-79.9 13.1-111.2-35.5L412.5 367.2zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256z" transform="translate(-256 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=50" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=50" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="50" title="Delete Class" onclick="deleteClass(50)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #49                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            2 : EduLearn Academy                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            BA2                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            BA2LP1                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Jul 30, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Different agents or only one agent - show detailed format -->
                                            
                                                                                        <div>
                                                <strong>Initial:</strong>
                                                3 : Rajesh R. Patel                                                <i class="bi bi-person-badge ms-1"></i>
                                            </div>
                                                                                                                                </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=49" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=49" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="49" title="Delete Class" onclick="deleteClass(49)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #48                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            2 : EduLearn Academy                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            SKILL                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            WALK                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Jul 14, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Different agents or only one agent - show detailed format -->
                                            
                                                                                        <div>
                                                <strong>Initial:</strong>
                                                1 : Michael M. van der Berg                                                <i class="bi bi-person-badge ms-1"></i>
                                            </div>
                                                                                                                                </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=48" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=48" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="48" title="Delete Class" onclick="deleteClass(48)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #47                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            5 : AgriGrow Farms                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            GETC                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            CL4                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Jul 7, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Different agents or only one agent - show detailed format -->
                                            
                                                                                        <div>
                                                <strong>Initial:</strong>
                                                3 : Rajesh R. Patel                                                <i class="bi bi-person-badge ms-1"></i>
                                            </div>
                                                                                                                                </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=47" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=47" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="47" title="Delete Class" onclick="deleteClass(47)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #46                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            2 : EduLearn Academy                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            AET                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            COMM_NUM                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Jul 7, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Different agents or only one agent - show detailed format -->
                                            
                                                                                        <div>
                                                <strong>Initial:</strong>
                                                10 : Fatima F. Ismail                                                <i class="bi bi-person-badge ms-1"></i>
                                            </div>
                                                                                                                                </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            Not Exam
                                            <svg class="svg-inline--fa fa-ban ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="ban" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg="" style="transform-origin: 0.5em 0.5em;"><g transform="translate(256 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M367.2 412.5L99.5 144.8C77.1 176.1 64 214.5 64 256c0 106 86 192 192 192c41.5 0 79.9-13.1 111.2-35.5zm45.3-45.3C434.9 335.9 448 297.5 448 256c0-106-86-192-192-192c-41.5 0-79.9 13.1-111.2 35.5L412.5 367.2zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256z" transform="translate(-256 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            Not SETA
                                            <svg class="svg-inline--fa fa-ban ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="ban" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg="" style="transform-origin: 0.5em 0.5em;"><g transform="translate(256 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M367.2 412.5L99.5 144.8C77.1 176.1 64 214.5 64 256c0 106 86 192 192 192c41.5 0 79.9-13.1 111.2-35.5zm45.3-45.3C434.9 335.9 448 297.5 448 256c0-106-86-192-192-192c-41.5 0-79.9 13.1-111.2-35.5L412.5 367.2zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256z" transform="translate(-256 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=46" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=46" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="46" title="Delete Class" onclick="deleteClass(46)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #45                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            2 : EduLearn Academy                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            AET                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            COMM_NUM                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Jul 7, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Different agents or only one agent - show detailed format -->
                                            
                                                                                        <div>
                                                <strong>Initial:</strong>
                                                3 : Rajesh R. Patel                                                <i class="bi bi-person-badge ms-1"></i>
                                            </div>
                                                                                                                                </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=45" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=45" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="45" title="Delete Class" onclick="deleteClass(45)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #44                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            2 : EduLearn Academy                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            AET                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            COMM_NUM                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Jul 7, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Different agents or only one agent - show detailed format -->
                                            
                                                                                        <div>
                                                <strong>Initial:</strong>
                                                3 : Rajesh R. Patel                                                <i class="bi bi-person-badge ms-1"></i>
                                            </div>
                                                                                                                                </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=44" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=44" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="44" title="Delete Class" onclick="deleteClass(44)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #43                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            2 : EduLearn Academy                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            AET                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            COMM_NUM                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Jul 7, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Different agents or only one agent - show detailed format -->
                                            
                                                                                        <div>
                                                <strong>Initial:</strong>
                                                3 : Rajesh R. Patel                                                <i class="bi bi-person-badge ms-1"></i>
                                            </div>
                                                                                                                                </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=43" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=43" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="43" title="Delete Class" onclick="deleteClass(43)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #42                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            2 : EduLearn Academy                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            AET                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            COMM_NUM                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Jul 7, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Different agents or only one agent - show detailed format -->
                                            
                                                                                        <div>
                                                <strong>Initial:</strong>
                                                2 : Thandi T. Nkosi                                                <i class="bi bi-person-badge ms-1"></i>
                                            </div>
                                                                                                                                </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=42" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=42" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="42" title="Delete Class" onclick="deleteClass(42)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #41                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            2 : EduLearn Academy                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            AET                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            COMM_NUM                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Jul 7, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Different agents or only one agent - show detailed format -->
                                            
                                                                                        <div>
                                                <strong>Initial:</strong>
                                                3 : Rajesh R. Patel                                                <i class="bi bi-person-badge ms-1"></i>
                                            </div>
                                                                                                                                </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=41" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=41" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="41" title="Delete Class" onclick="deleteClass(41)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #40                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            2 : EduLearn Academy                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            AET                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            COMM_NUM                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Jul 7, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Different agents or only one agent - show detailed format -->
                                            
                                                                                        <div>
                                                <strong>Initial:</strong>
                                                3 : Rajesh R. Patel                                                <i class="bi bi-person-badge ms-1"></i>
                                            </div>
                                                                                                                                </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=40" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=40" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="40" title="Delete Class" onclick="deleteClass(40)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #39                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            2 : EduLearn Academy                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            AET                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            COMM_NUM                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Jul 8, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Different agents or only one agent - show detailed format -->
                                            
                                                                                        <div>
                                                <strong>Initial:</strong>
                                                7 : David D. O'Connor                                                <i class="bi bi-person-badge ms-1"></i>
                                            </div>
                                                                                                                                </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=39" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=39" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="39" title="Delete Class" onclick="deleteClass(39)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #38                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            5 : AgriGrow Farms                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            AET                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            COMM_NUM                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Jul 7, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Different agents or only one agent - show detailed format -->
                                            
                                                                                        <div>
                                                <strong>Initial:</strong>
                                                3 : Rajesh R. Patel                                                <i class="bi bi-person-badge ms-1"></i>
                                            </div>
                                                                                                                                </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=38" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=38" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="38" title="Delete Class" onclick="deleteClass(38)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #37                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            2 : EduLearn Academy                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            AET                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            COMM_NUM                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Jul 7, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Different agents or only one agent - show detailed format -->
                                            
                                                                                        <div>
                                                <strong>Initial:</strong>
                                                2 : Thandi T. Nkosi                                                <i class="bi bi-person-badge ms-1"></i>
                                            </div>
                                                                                                                                </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=37" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=37" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="37" title="Delete Class" onclick="deleteClass(37)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #36                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            2 : EduLearn Academy                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            AET                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            COMM_NUM                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Jul 7, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Different agents or only one agent - show detailed format -->
                                            
                                                                                        <div>
                                                <strong>Initial:</strong>
                                                2 : Thandi T. Nkosi                                                <i class="bi bi-person-badge ms-1"></i>
                                            </div>
                                                                                                                                </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=36" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=36" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="36" title="Delete Class" onclick="deleteClass(36)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="display: none;">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #35                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            2 : EduLearn Academy                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            AET                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            COMM_NUM                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Jun 30, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Different agents or only one agent - show detailed format -->
                                            
                                                                                        <div>
                                                <strong>Initial:</strong>
                                                1 : Michael M. van der Berg                                                <i class="bi bi-person-badge ms-1"></i>
                                            </div>
                                                                                                                                </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=35" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=35" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="35" title="Delete Class" onclick="deleteClass(35)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="display: none;">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #34                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            2 : EduLearn Academy                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            AET                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            COMM_NUM                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Jun 30, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Different agents or only one agent - show detailed format -->
                                            
                                                                                        <div>
                                                <strong>Initial:</strong>
                                                1 : Michael M. van der Berg                                                <i class="bi bi-person-badge ms-1"></i>
                                            </div>
                                                                                                                                </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=34" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=34" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="34" title="Delete Class" onclick="deleteClass(34)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="display: none;">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #33                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            2 : EduLearn Academy                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            AET                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            COMM_NUM                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Jun 30, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Different agents or only one agent - show detailed format -->
                                            
                                                                                        <div>
                                                <strong>Initial:</strong>
                                                1 : Michael M. van der Berg                                                <i class="bi bi-person-badge ms-1"></i>
                                            </div>
                                                                                                                                </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=33" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=33" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="33" title="Delete Class" onclick="deleteClass(33)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="display: none;">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #32                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            14 : FinanceFirst Corp - KZN Branch                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            REALLL                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            RLC                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Jun 9, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Same agent for both current and initial - show simplified format -->
                                            <div>
                                                1 : Michael M. van der Berg                                                <i class="bi bi-person-circle ms-1"></i>
                                            </div>
                                                                                    </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=32" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=32" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="32" title="Delete Class" onclick="deleteClass(32)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="display: none;">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #31                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            11 : RetailPro Stores                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            AET                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            COMM_NUM                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Jun 9, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Same agent for both current and initial - show simplified format -->
                                            <div>
                                                1 : Michael M. van der Berg                                                <i class="bi bi-person-circle ms-1"></i>
                                            </div>
                                                                                    </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=31" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=31" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="31" title="Delete Class" onclick="deleteClass(31)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="display: none;">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #30                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            14 : FinanceFirst Corp - KZN Branch                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            GETC                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            SMME4                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Jun 9, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Same agent for both current and initial - show simplified format -->
                                            <div>
                                                1 : Michael M. van der Berg                                                <i class="bi bi-person-circle ms-1"></i>
                                            </div>
                                                                                    </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=30" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=30" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="30" title="Delete Class" onclick="deleteClass(30)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="display: none;">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #29                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            14 : FinanceFirst Corp - KZN Branch                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            GETC                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            SMME4                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Jun 9, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Same agent for both current and initial - show simplified format -->
                                            <div>
                                                1 : Michael M. van der Berg                                                <i class="bi bi-person-circle ms-1"></i>
                                            </div>
                                                                                    </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=29" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=29" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="29" title="Delete Class" onclick="deleteClass(29)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="display: none;">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #28                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            14 : FinanceFirst Corp - KZN Branch                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            REALLL                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            RLC                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Jun 9, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Same agent for both current and initial - show simplified format -->
                                            <div>
                                                1 : Michael M. van der Berg                                                <i class="bi bi-person-circle ms-1"></i>
                                            </div>
                                                                                    </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=28" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=28" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="28" title="Delete Class" onclick="deleteClass(28)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="display: none;">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #27                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            14 : FinanceFirst Corp - KZN Branch                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            REALLL                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            RLC                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Jun 9, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Same agent for both current and initial - show simplified format -->
                                            <div>
                                                1 : Michael M. van der Berg                                                <i class="bi bi-person-circle ms-1"></i>
                                            </div>
                                                                                    </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=27" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=27" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="27" title="Delete Class" onclick="deleteClass(27)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="display: none;">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #26                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            14 : FinanceFirst Corp - KZN Branch                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            AET                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            COMM_NUM                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Jun 9, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Same agent for both current and initial - show simplified format -->
                                            <div>
                                                10 : Fatima F. Ismail                                                <i class="bi bi-person-circle ms-1"></i>
                                            </div>
                                                                                    </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=26" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=26" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="26" title="Delete Class" onclick="deleteClass(26)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="display: none;">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #24                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            11 : RetailPro Stores                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            AET                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            BOTH                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Jun 2, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Same agent for both current and initial - show simplified format -->
                                            <div>
                                                1 : Michael M. van der Berg                                                <i class="bi bi-person-circle ms-1"></i>
                                            </div>
                                                                                    </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=24" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=24" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="24" title="Delete Class" onclick="deleteClass(24)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="display: none;">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #23                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            11 : RetailPro Stores                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            AET                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            BOTH                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Jun 2, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Same agent for both current and initial - show simplified format -->
                                            <div>
                                                1 : Michael M. van der Berg                                                <i class="bi bi-person-circle ms-1"></i>
                                            </div>
                                                                                    </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=23" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=23" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="23" title="Delete Class" onclick="deleteClass(23)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="display: none;">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #22                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            11 : RetailPro Stores                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            AET                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            BOTH                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Jun 2, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Same agent for both current and initial - show simplified format -->
                                            <div>
                                                1 : Michael M. van der Berg                                                <i class="bi bi-person-circle ms-1"></i>
                                            </div>
                                                                                    </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=22" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=22" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="22" title="Delete Class" onclick="deleteClass(22)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="display: none;">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #21                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            11 : RetailPro Stores                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            AET                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            BOTH                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Jun 2, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Same agent for both current and initial - show simplified format -->
                                            <div>
                                                1 : Michael M. van der Berg                                                <i class="bi bi-person-circle ms-1"></i>
                                            </div>
                                                                                    </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=21" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=21" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="21" title="Delete Class" onclick="deleteClass(21)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="display: none;">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #20                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            11 : RetailPro Stores                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            AET                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            BOTH                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Jun 2, 2025                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <div class="text-nowrap">
                                                                                        <!-- Different agents or only one agent - show detailed format -->
                                            
                                                                                        <div>
                                                <strong>Initial:</strong>
                                                1 : Michael M. van der Berg                                                <i class="bi bi-person-badge ms-1"></i>
                                            </div>
                                                                                                                                </div>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=20" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=20" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="20" title="Delete Class" onclick="deleteClass(20)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="display: none;">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #15                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            15 : HealthCare Plus - Eastern Cape                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            Specialized                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            No Subject                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Aug 15, 2023                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-warning">
                                            No Agent Assigned
                                            <i class="bi bi-exclamation-triangle ms-1"></i>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            Not Exam
                                            <svg class="svg-inline--fa fa-ban ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="ban" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg="" style="transform-origin: 0.5em 0.5em;"><g transform="translate(256 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M367.2 412.5L99.5 144.8C77.1 176.1 64 214.5 64 256c0 106 86 192 192 192c41.5 0 79.9-13.1 111.2-35.5zm45.3-45.3C434.9 335.9 448 297.5 448 256c0-106-86-192-192-192c-41.5 0-79.9 13.1-111.2 35.5L412.5 367.2zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256z" transform="translate(-256 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=15" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=15" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="15" title="Delete Class" onclick="deleteClass(15)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="display: none;">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #14                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            14 : FinanceFirst Corp - KZN Branch                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            Corporate                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            No Subject                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Jul 15, 2023                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-warning">
                                            No Agent Assigned
                                            <i class="bi bi-exclamation-triangle ms-1"></i>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=14" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=14" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="14" title="Delete Class" onclick="deleteClass(14)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="display: none;">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #13                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            13 : EduLearn Academy - Paarl Campus                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            Community                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            No Subject                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Aug 1, 2023                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-warning">
                                            No Agent Assigned
                                            <i class="bi bi-exclamation-triangle ms-1"></i>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            Not Exam
                                            <svg class="svg-inline--fa fa-ban ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="ban" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg="" style="transform-origin: 0.5em 0.5em;"><g transform="translate(256 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M367.2 412.5L99.5 144.8C77.1 176.1 64 214.5 64 256c0 106 86 192 192 192c41.5 0 79.9-13.1 111.2-35.5zm45.3-45.3C434.9 335.9 448 297.5 448 256c0-106-86-192-192-192c-41.5 0-79.9 13.1-111.2 35.5L412.5 367.2zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256z" transform="translate(-256 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=13" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=13" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="13" title="Delete Class" onclick="deleteClass(13)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="display: none;">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #10                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            10 : ConstructBuild Ltd                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            Corporate                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            No Subject                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Jun 1, 2023                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-warning">
                                            No Agent Assigned
                                            <i class="bi bi-exclamation-triangle ms-1"></i>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=10" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=10" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="10" title="Delete Class" onclick="deleteClass(10)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="display: none;">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #9                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            9 : GreenEnergy Solutions                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            Specialized                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            No Subject                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            May 15, 2023                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-warning">
                                            No Agent Assigned
                                            <i class="bi bi-exclamation-triangle ms-1"></i>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            Not Exam
                                            <svg class="svg-inline--fa fa-ban ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="ban" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg="" style="transform-origin: 0.5em 0.5em;"><g transform="translate(256 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M367.2 412.5L99.5 144.8C77.1 176.1 64 214.5 64 256c0 106 86 192 192 192c41.5 0 79.9-13.1 111.2-35.5zm45.3-45.3C434.9 335.9 448 297.5 448 256c0-106-86-192-192-192c-41.5 0-79.9 13.1-111.2 35.5L412.5 367.2zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256z" transform="translate(-256 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=9" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=9" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="9" title="Delete Class" onclick="deleteClass(9)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="display: none;">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #7                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            7 : MiningPro Resources                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            Specialized                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            No Subject                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Apr 15, 2023                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-warning">
                                            No Agent Assigned
                                            <i class="bi bi-exclamation-triangle ms-1"></i>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            Not Exam
                                            <svg class="svg-inline--fa fa-ban ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="ban" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg="" style="transform-origin: 0.5em 0.5em;"><g transform="translate(256 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M367.2 412.5L99.5 144.8C77.1 176.1 64 214.5 64 256c0 106 86 192 192 192c41.5 0 79.9-13.1 111.2-35.5zm45.3-45.3C434.9 335.9 448 297.5 448 256c0-106-86-192-192-192c-41.5 0-79.9 13.1-111.2 35.5L412.5 367.2zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256z" transform="translate(-256 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=7" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=7" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="7" title="Delete Class" onclick="deleteClass(7)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="display: none;">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #6                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            6 : HealthCare Plus                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            Corporate                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            No Subject                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Apr 1, 2023                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-warning">
                                            No Agent Assigned
                                            <i class="bi bi-exclamation-triangle ms-1"></i>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=6" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=6" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="6" title="Delete Class" onclick="deleteClass(6)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="display: none;">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #5                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            5 : AgriGrow Farms                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            Specialized                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            No Subject                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Mar 15, 2023                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-warning">
                                            No Agent Assigned
                                            <i class="bi bi-exclamation-triangle ms-1"></i>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            Not Exam
                                            <svg class="svg-inline--fa fa-ban ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="ban" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg="" style="transform-origin: 0.5em 0.5em;"><g transform="translate(256 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M367.2 412.5L99.5 144.8C77.1 176.1 64 214.5 64 256c0 106 86 192 192 192c41.5 0 79.9-13.1 111.2-35.5zm45.3-45.3C434.9 335.9 448 297.5 448 256c0-106-86-192-192-192c-41.5 0-79.9 13.1-111.2 35.5L412.5 367.2zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256z" transform="translate(-256 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=5" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=5" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="5" title="Delete Class" onclick="deleteClass(5)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="display: none;">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #3                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            3 : IndustrialTech Ltd                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            Corporate                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            No Subject                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Mar 1, 2023                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-warning">
                                            No Agent Assigned
                                            <i class="bi bi-exclamation-triangle ms-1"></i>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            Exam Class
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            Not SETA
                                            <svg class="svg-inline--fa fa-ban ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="ban" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg="" style="transform-origin: 0.5em 0.5em;"><g transform="translate(256 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M367.2 412.5L99.5 144.8C77.1 176.1 64 214.5 64 256c0 106 86 192 192 192c41.5 0 79.9-13.1 111.2-35.5zm45.3-45.3C434.9 335.9 448 297.5 448 256c0-106-86-192-192-192c-41.5 0-79.9 13.1-111.2-35.5L412.5 367.2zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256z" transform="translate(-256 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=3" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=3" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="3" title="Delete Class" onclick="deleteClass(3)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                                <tr style="display: none;">
                                    <td class="py-2 align-middle text-center fs-8 white-space-nowrap">
                                        <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            #1                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">
                                            1 : TechCorp Solutions                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                            Corporate                                        </span>
                                                                            </td>
                                    <td>
                                        <span class="fw-medium">
                                            No Subject                                        </span>
                                    </td>
                                    <td>
                                                                                <span class="text-nowrap">
                                            Feb 1, 2023                                        </span>
                                                                            </td>
                                    <td>
                                        
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-warning">
                                            No Agent Assigned
                                            <i class="bi bi-exclamation-triangle ms-1"></i>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-secondary">
                                            Not Exam
                                            <svg class="svg-inline--fa fa-ban ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="ban" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg="" style="transform-origin: 0.5em 0.5em;"><g transform="translate(256 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M367.2 412.5L99.5 144.8C77.1 176.1 64 214.5 64 256c0 106 86 192 192 192c41.5 0 79.9-13.1 111.2-35.5zm45.3-45.3C434.9 335.9 448 297.5 448 256c0-106-86-192-192-192c-41.5 0-79.9 13.1-111.2 35.5L412.5 367.2zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256z" transform="translate(-256 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge badge-phoenix fs-10 badge-phoenix-success">
                                            <span class="badge-label">Active</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check ms-1" style="height:12.8px;width:12.8px;">
                                                <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                        </span>
                                                                            </td>
                                    <td class="py-2 fs-8 white-space-nowrap">
                                                                                <span class="badge fs-10 badge-phoenix badge-phoenix-success">
                                            SETA Funded
                                            <svg class="svg-inline--fa fa-check ms-1" data-fa-transform="shrink-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg="" style="transform-origin: 0.4375em 0.5em;"><g transform="translate(224 256)"><g transform="translate(0, 0)  scale(0.875, 0.875)  rotate(0 0 0)"><path fill="currentColor" d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z" transform="translate(-224 -256)"></path></g></g></svg>
                                        </span>
                                                                            </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-2" role="group">
                                            <a href="http://localhost/wecoza/app/display-single-class/?class_id=1" class="btn btn-sm btn-outline-secondary border-0" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                                                                        <a href="http://localhost/wecoza/app/new-class/?mode=update&amp;class_id=1" class="btn btn-sm btn-outline-secondary border-0" title="Edit Class">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                                                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary border-0 delete-class-btn" data-class-id="1" title="Delete Class" onclick="deleteClass(1)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                                                                    </div>
                                    </td>
                                </tr>
                                                            </tbody>
                        </table>
                    </div><div id="classes-pagination" class="d-flex justify-content-between mt-3"><span class="d-none d-sm-inline-block" data-list-info="data-list-info">
                1 to 20 <span class="text-body-tertiary"> Items of </span>45
            </span><nav aria-label="Classes pagination"><ul class="pagination pagination-sm"><li class="page-item disabled">
                <span class="page-link" aria-hidden="true">«</span>
            </li><li class="page-item active" aria-current="page">
                    <span class="page-link">1</span>
                </li><li class="page-item">
                    <a class="page-link" href="#" data-page-number="2">2</a>
                </li><li class="page-item">
                    <a class="page-link" href="#" data-page-number="3">3</a>
                </li><li class="page-item">
                <a class="page-link" href="#" data-list-pagination="next" aria-label="Next">
                    <span aria-hidden="true">»</span>
                </a>
            </li></ul></nav></div>
                </div>
            </div>